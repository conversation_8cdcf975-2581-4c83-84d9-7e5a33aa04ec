////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OversetKDTManager.h
//! <AUTHOR>
//! @brief 重叠网格KDT管理器
//! @date 2024-03-12
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_OversetKDTManager_
#define _specialModule_oversetMesh_OversetKDTManager_

#include "meshProcess/wallDistance/KDT_utilities.h"
#include "meshProcess/zone/ZoneManager.h"
#include "basic/mesh/Mesh.h"
#include "basic/dataStruct/List.h"
#include <memory>
#include <queue>

namespace Overset
{
    /**
     * @brief KDT搜索器管理器
     * 
     * 负责KDT搜索器的创建、销毁、池化管理和生命周期控制
     */
    class OversetKDTManager
    {
    public:
        /**
         * @brief 构造函数
         * @param dimension 网格维度
         * @param zoneManager 域管理器指针
         */
        OversetKDTManager(int dimension, ZoneManager* zoneManager);
        
        /**
         * @brief 析构函数
         */
        ~OversetKDTManager();
        
        /**
         * @brief 初始化所有子域的KDT搜索器
         * @param localMesh 本地网格指针
         * @param nZones 子域数量
         */
        void initializeKDTSearchers(Mesh* localMesh, int nZones);
        
        /**
         * @brief 获取指定子域的KDT搜索器
         * @param zoneID 子域编号
         * @return KDT搜索器指针，如果不存在返回nullptr
         */
        KDT* getKDTSearcher(int zoneID) const;
        
        /**
         * @brief 清理所有KDT搜索器
         */
        void clear();
        
        /**
         * @brief 重建指定子域的KDT树
         * @param zoneID 子域编号
         * @param localMesh 本地网格指针
         * @return 是否成功重建
         */
        bool rebuildKDTTree(int zoneID, Mesh* localMesh);
        
        /**
         * @brief 获取全局树信息
         * @return 全局树信息向量
         */
        const std::vector<TreeInfo>& getGlobalTreeInfo() const;
        
        /**
         * @brief 获取KDT搜索器数量
         */
        size_t getSearcherCount() const { return kdtSearchers.size(); }

    private:
        /**
         * @brief KDT搜索器池
         */
        class KDTSearcherPool
        {
        private:
            std::vector<std::unique_ptr<KDT>> pool;
            std::queue<KDT*> available;
            int dimension;

        public:
            KDTSearcherPool(int dim) : dimension(dim) {}
            
            KDT* acquire();
            void release(KDT* searcher);
            void clear();
        };
        
        /**
         * @brief 创建指定子域的网格
         * @param zoneID 子域编号
         * @param localMesh 本地网格指针
         * @return 子域网格指针
         */
        Mesh* createZoneMesh(int zoneID, Mesh* localMesh);

    private:
        int dimension;                              // 网格维度
        ZoneManager* zoneManager;                   // 域管理器指针
        List<KDT*> kdtSearchers;                   // 各子域的KDT搜索器
        std::unique_ptr<KDTSearcherPool> kdtPool;  // KDT搜索器池
        mutable std::vector<TreeInfo> cachedGlobalTreeInfo; // 缓存的全局树信息
        mutable bool treeInfoNeedsUpdate = true;   // 树信息是否需要更新
    };
}

#endif

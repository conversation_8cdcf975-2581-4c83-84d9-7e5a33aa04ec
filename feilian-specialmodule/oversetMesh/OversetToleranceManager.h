////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OversetToleranceManager.h
//! <AUTHOR>
//! @brief 重叠网格容差管理器
//! @date 2024-03-12
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_OversetToleranceManager_
#define _specialModule_oversetMesh_OversetToleranceManager_

#include "basic/mesh/Mesh.h"
#include "basic/common/ConfigUtility.h"
#include <unordered_map>

namespace Overset
{
    /**
     * @brief 重叠网格容差管理器
     * 
     * 负责自适应容差计算和管理，提供基于网格特征的容差调整
     */
    class OversetToleranceManager
    {
    public:
        /**
         * @brief 容差计算策略
         */
        enum class ToleranceStrategy
        {
            FIXED,              // 固定容差
            MESH_ADAPTIVE,      // 基于网格特征的自适应容差
            ELEMENT_ADAPTIVE,   // 基于单元尺寸的自适应容差
            HYBRID             // 混合策略
        };

    public:
        /**
         * @brief 构造函数
         * @param baseTolerance 基础容差值
         * @param strategy 容差计算策略
         */
        OversetToleranceManager(Scalar baseTolerance = 1.0e-10,
                              ToleranceStrategy strategy = ToleranceStrategy::MESH_ADAPTIVE);
        
        /**
         * @brief 析构函数
         */
        ~OversetToleranceManager() = default;
        
        /**
         * @brief 初始化容差管理器
         * @param localMesh 本地网格指针
         */
        void initialize(Mesh* localMesh);
        
        /**
         * @brief 获取指定单元的容差
         * @param elem 网格单元
         * @return 容差值
         */
        Scalar getToleranceForElement(const Element& elem);
        
        /**
         * @brief 获取指定单元ID的容差
         * @param elemID 单元ID
         * @param localMesh 本地网格指针
         * @return 容差值
         */
        Scalar getToleranceForElement(int elemID, Mesh* localMesh);
        
        /**
         * @brief 手动设置指定单元的容差
         * @param elemID 单元ID
         * @param tolerance 容差值
         */
        void setElementTolerance(int elemID, Scalar tolerance);
        
        /**
         * @brief 设置网格特征长度
         * @param length 特征长度
         */
        void setMeshCharacteristicLength(Scalar length);
        
        /**
         * @brief 获取网格特征长度
         */
        Scalar getMeshCharacteristicLength() const { return meshCharacteristicLength; }
        
        /**
         * @brief 设置基础容差
         * @param tolerance 基础容差值
         */
        void setBaseTolerance(Scalar tolerance) { baseTolerance = tolerance; }
        
        /**
         * @brief 获取基础容差
         */
        Scalar getBaseTolerance() const { return baseTolerance; }
        
        /**
         * @brief 设置容差计算策略
         * @param strategy 容差策略
         */
        void setToleranceStrategy(ToleranceStrategy strategy) { currentStrategy = strategy; }
        
        /**
         * @brief 获取容差计算策略
         */
        ToleranceStrategy getToleranceStrategy() const { return currentStrategy; }
        
        /**
         * @brief 清理缓存的容差数据
         */
        void clearToleranceCache();
        
        /**
         * @brief 获取缓存的容差数量
         */
        size_t getCachedToleranceCount() const { return elementTolerances.size(); }
        
        /**
         * @brief 计算网格特征长度
         * @param localMesh 本地网格指针
         * @return 特征长度
         */
        static Scalar calculateMeshCharacteristicLength(Mesh* localMesh);

    private:
        /**
         * @brief 基于固定值计算容差
         */
        Scalar calculateFixedTolerance() const;
        
        /**
         * @brief 基于网格特征计算容差
         */
        Scalar calculateMeshAdaptiveTolerance() const;
        
        /**
         * @brief 基于单元尺寸计算容差
         */
        Scalar calculateElementAdaptiveTolerance(const Element& elem) const;
        
        /**
         * @brief 基于混合策略计算容差
         */
        Scalar calculateHybridTolerance(const Element& elem) const;

    private:
        Scalar baseTolerance;                      // 基础容差值
        Scalar meshCharacteristicLength = 1.0;    // 网格特征长度
        ToleranceStrategy currentStrategy;         // 当前容差策略
        
        // 缓存的单元容差
        std::unordered_map<int, Scalar> elementTolerances;
        
        // 策略相关参数
        Scalar relativeTolerance = 1.0e-6;        // 相对容差系数
        Scalar minTolerance = 1.0e-12;            // 最小容差值
        Scalar maxTolerance = 1.0e-6;             // 最大容差值
    };
}

#endif

////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OversetWallDistanceCalculator.h
//! <AUTHOR>
//! @brief 重叠网格壁面距离计算器
//! @date 2024-12-19
//
//------------------------------修改日志----------------------------------------
// 2024-12-19 曾凯
//    说明：从OversetMesh类中分离壁面距离计算功能，提高代码模块化程度
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_OversetWallDistanceCalculator_
#define _specialModule_oversetMesh_OversetWallDistanceCalculator_

#include "meshProcess/zone/ZoneManager.h"
#include "meshProcess/wallDistance/WallDistanceManager.h"
#include "sourceFlow/configure/FlowConfigure.h"
#include "feilian-specialmodule/oversetMesh/OverDefines.h"
#include "feilian-external/linux-gcc7.3.0/boost/include/boost/mpi.hpp"
#include <chrono>
#include <memory>
#include <unordered_map>
#include <unordered_set>

namespace Overset
{
    // 前向声明
    class OversetDonorSearcher;

    /**
     * @brief 重叠网格壁面距离计算器
     *
     * 负责计算网格单元到各子域壁面的距离，支持KDT树加速和并行计算
     */
    class WallDistCalculator
    {
    public:
        /**
         * @brief 构造函数
         *
         * @param mesh_ 网格指针
         * @param zoneManager_ 域管理器指针
         * @param flowConfig_ 流场配置
         * @param mpi_world_ MPI通信器
         */
        WallDistCalculator(Mesh *mesh_,
                           ZoneManager *zoneManager_,
                           const Configure::Flow::FlowConfigure &flowConfig_,
                           const boost::mpi::communicator &mpi_world_);

        /**
         * @brief 析构函数
         */
        ~WallDistCalculator();

        /**
         * @brief 初始化壁面距离计算器
         *
         * @param wallDistMethod 壁面距离计算方法
         * @param elemTypeMethod 单元类型判断方法
         */
        void Initialize(Turbulence::WallDistance wallDistMethod,
                        AssembleMethod elemTypeMethod);

        /**
         * @brief 计算所有单元到各子域壁面的距离
         *
         * @param donorSearcher 贡献单元搜索器指针（用于距离修正）
         */
        void CalculateWallDistances(OversetDonorSearcher *donorSearcher = nullptr);

        /**
         * @brief 更新网格中的壁面距离场
         */
        void UpdateWallDistField();

        /**
         * @brief 获取指定单元到指定子域的壁面距离
         *
         * @param elemID 单元编号
         * @param zoneID 子域编号
         * @return Scalar 壁面距离
         */
        Scalar GetWallDistance(int elemID, int zoneID) const;

        /**
         * @brief 判断指定单元的最短壁面距离是否来自自身子域
         *
         * @param elemID 单元编号
         * @param elemZoneID 单元所在子域编号
         * @return true 最短距离来自自身子域
         * @return false 最短距离来自其他子域
         */
        bool IsNearestWallDistToSelf(int elemID, int elemZoneID) const;

        /**
         * @brief 获取所有壁面距离数据（只读访问）
         *
         * @return const List<List<Scalar>>& 壁面距离数据
         */
        const List<List<Scalar>> &GetWallDistances() const { return wallDistances; }

        /**
         * @brief 获取最近壁面面元ID数据（只读访问）
         *
         * @return const List<List<int>>& 最近壁面面元ID数据
         */
        const List<List<int>> &GetNearestWallFaceIDs() const { return nearestWallFaceID; }

        /**
         * @brief 清理计算数据
         */
        void Clear();

        // ==================== 动态网格支持接口 ====================

        /**
         * @brief 启用动态网格模式
         * @param rebuildThreshold 重建阈值（相对位移）
         * @param cacheValidityTime 缓存有效时间（秒）
         */
        void EnableDynamicMesh(Scalar rebuildThreshold = 0.1, double cacheValidityTime = 1.0);

        /**
         * @brief 更新网格运动信息
         * @param displacementField 位移场
         * @param timeStep 时间步长
         * @param stepNumber 时间步编号
         */
        void UpdateMeshMotion(const std::vector<Vector> &displacementField,
                              double timeStep, int stepNumber);

        /**
         * @brief 增量更新壁面距离
         * @param forceFullUpdate 是否强制完全更新
         * @return 是否成功增量更新（false表示需要完全重建）
         */
        bool IncrementalUpdateWallDistances(bool forceFullUpdate = false);

        /**
         * @brief 获取动态网格统计信息
         */
        struct DynamicWallDistanceMetrics
        {
            size_t incrementalUpdates = 0;
            size_t fullRebuilds = 0;
            size_t cacheHits = 0;
            size_t cacheMisses = 0;
            size_t inheritanceHits = 0;
            double totalUpdateTime = 0.0;
            double averageDisplacement = 0.0;
            double maxDisplacement = 0.0;
            double cacheHitRate() const { return (cacheHits + cacheMisses) > 0 ? (double)cacheHits / (cacheHits + cacheMisses) : 0.0; }
        };
        const DynamicWallDistanceMetrics &GetDynamicMetrics() const { return dynamicMetrics; }

        /**
         * @brief 重置动态网格统计信息
         */
        void ResetDynamicMetrics() { dynamicMetrics = DynamicWallDistanceMetrics{}; }

    private:
        /**
         * @brief 收集全局壁面边界面元
         */
        void CollectGlobalWallFaces();

        /**
         * @brief 改进的点在壁面内部判断方法
         *
         * @param point 目标点坐标
         * @param nearestFace 最近壁面面元
         * @param faceNodes 面元节点列表
         * @param zoneID 子域编号
         * @param elemZoneID 单元所在子域编号
         * @return true 在壁面内部
         * @return false 在壁面外部
         */
        bool IsPointInsideWallImproved(const Vector &point,
                                       const Face &nearestFace,
                                       const std::vector<Node> &faceNodes,
                                       int zoneID,
                                       int elemZoneID);

        /**
         * @brief 射线投射法判断点是否在壁面内部
         */
        bool IsPointInsideWallRayCasting(const Vector &point, int zoneID);

        /**
         * @brief 改进法向量法判断点是否在壁面内部
         */
        bool IsPointInsideWallImprovedNormal(const Vector &point,
                                             const Face &nearestFace,
                                             const std::vector<Node> &faceNodes,
                                             bool isSameZone);

        /**
         * @brief 拓扑连通性法判断点是否在壁面内部
         */
        bool IsPointInsideWallTopology(const Vector &point, int zoneID);

        /**
         * @brief 混合方法判断点是否在壁面内部
         */
        bool IsPointInsideWallHybrid(const Vector &point,
                                     const Face &nearestFace,
                                     const std::vector<Node> &faceNodes,
                                     int zoneID,
                                     int elemZoneID);

        // 辅助方法
        /**
         * @brief 判断点是否在多边形内部
         */
        bool IsPointInPolygon(const Vector &point,
                              const Face &face,
                              const std::vector<Node> &faceNodes);

        /**
         * @brief 计算几何置信度
         */
        Scalar CalculateGeometricConfidence(const Vector &point,
                                            const Face &nearestFace,
                                            const std::vector<Node> &faceNodes);

        /**
         * @brief 计算局部曲率修正
         */
        Scalar CalculateLocalCurvatureCorrection(const Face &nearestFace,
                                                 const std::vector<Node> &faceNodes);

        /**
         * @brief 找到包含指定点的网格单元
         */
        int FindContainingElement(const Vector &point);

        /**
         * @brief 判断单元是否被壁面包围
         */
        bool IsElementSurroundedByWall(int elemID, int zoneID);

        /**
         * @brief 对计算出的壁面距离进行法向修正（改进版）
         *
         * @param donorSearcher 贡献单元搜索器指针（可选，用于兼容性）
         */
        void CorrectWallDist(OversetDonorSearcher *donorSearcher = nullptr);

        /**
         * @brief 使用改进的几何方法修正壁面距离
         */
        void CorrectWallDistImproved();

    public:
        /**
         * @brief 壁面距离修正方法枚举
         */
        enum class WallDistCorrectionMethod
        {
            DONOR_SEARCH_BASED, // 基于DonorSearch的传统方法（兼容性）
            RAY_CASTING,        // 射线投射法
            IMPROVED_NORMAL,    // 改进法向量法
            TOPOLOGY_BASED,     // 拓扑连通性法
            HYBRID_METHOD       // 混合方法
        };

        /**
         * @brief 设置壁面距离修正方法
         *
         * @param method 修正方法类型
         */
        void SetCorrectionMethod(WallDistCorrectionMethod method);

    private:
        // 基础数据
        Mesh *localMesh;                                  // 当前进程网格指针
        ZoneManager *zoneManager;                         // 域管理器指针
        const Configure::Flow::FlowConfigure &flowConfig; // 流场配置
        const boost::mpi::communicator &mpi_world;        // MPI通信器
        int n_Zones;                                      // 网格子域个数
        Mesh::MeshDim dim;                                // 网格维度
        int processorID;                                  // 当前进程ID
        int nProcessor;                                   // 总进程数

        // 计算方法配置
        Turbulence::WallDistance wallDistMethod;   // 壁面距离计算方法
        AssembleMethod elemTypeMethod;             // 单元类型判断方法
        WallDistCorrectionMethod correctionMethod; // 壁面距离修正方法

        // 壁面距离管理器
        WallDistanceManager *wallDistanceManager; // 壁面距离计算管理器

        // 壁面数据（用于重叠网格特殊处理）
        List<std::vector<std::pair<Face, std::vector<Node>>>> globalWallFaceVectors; // 各子域的壁面面元向量

        // 计算结果
        List<List<Scalar>> wallDistances;  // 网格点或单元到各子域壁面的最短距离
        List<List<int>> nearestWallFaceID; // 最近壁面面元ID

        // ==================== 动态网格支持数据结构 ====================

        // 动态网格状态
        bool isDynamicMeshEnabled = false;
        Scalar rebuildThreshold = 0.1;
        double cacheValidityTime = 1.0;
        double currentTimeStep = 0.0;
        int currentStepNumber = 0;
        mutable DynamicWallDistanceMetrics dynamicMetrics;

        // 网格运动信息
        std::vector<Vector> previousPositions;
        std::vector<Vector> currentDisplacements;
        std::vector<Scalar> elementDisplacements; // 每个单元的位移幅度

        // 前向声明内部管理器类
        class DynamicWallDistanceManager;
        class IncrementalWallDistanceUpdater;
        class WallDistanceCache;
        class TimeStepInheritanceManager;
        class DynamicLoadBalancer;

        // 动态网格管理器实例
        std::unique_ptr<DynamicWallDistanceManager> dynamicManager;
        std::unique_ptr<IncrementalWallDistanceUpdater> incrementalUpdater;
        std::unique_ptr<WallDistanceCache> resultCache;
        std::unique_ptr<TimeStepInheritanceManager> inheritanceManager;
        std::unique_ptr<DynamicLoadBalancer> loadBalancer;

        // ==================== 动态网格辅助方法 ====================

        /**
         * @brief 分析网格运动模式
         */
        enum MotionType
        {
            RIGID_BODY,
            DEFORMATION,
            MIXED
        };
        MotionType AnalyzeMotionType(const std::vector<Vector> &displacements) const;

        /**
         * @brief 计算单元位移幅度
         */
        void CalculateElementDisplacements(const std::vector<Vector> &displacements);

        /**
         * @brief 智能壁面距离计算策略（结合缓存和继承）
         */
        void SmartWallDistanceCalculation();

        /**
         * @brief 执行实际的壁面距离计算
         */
        void PerformActualWallDistanceCalculation(const std::vector<int> &elementsToUpdate);

        /**
         * @brief 计算单元速度
         */
        Vector CalculateVelocity(int elemID) const;
    };
}

#endif

#include "feilian-specialmodule/oversetMesh/OversetWallDistanceCalculator.h"
#include <iostream>

/**
 * @brief 动态重叠网格壁面距离计算器使用示例
 * 
 * 本示例展示如何使用改进后的OversetWallDistanceCalculator
 * 来处理动态重叠网格场景，包括网格运动、增量更新等功能。
 */

namespace Overset
{
    class DynamicWallDistanceUsageExample
    {
    private:
        Mesh *mesh;
        ZoneManager *zoneManager;
        Configure::Flow::FlowConfigure flowConfig;
        boost::mpi::communicator mpi_world;
        std::unique_ptr<WallDistCalculator> wallDistCalculator;

    public:
        DynamicWallDistanceUsageExample(Mesh *mesh_, ZoneManager *zoneManager_, 
                                       const Configure::Flow::FlowConfigure &flowConfig_)
            : mesh(mesh_), zoneManager(zoneManager_), flowConfig(flowConfig_)
        {
            // 创建壁面距离计算器
            wallDistCalculator = std::make_unique<WallDistCalculator>(mesh, zoneManager, flowConfig, mpi_world);
        }

        /**
         * @brief 基本使用示例 - 静态网格
         */
        void basicUsageExample()
        {
            std::cout << "=== 基本使用示例 - 静态网格 ===" << std::endl;

            // 1. 初始化计算器
            wallDistCalculator->Initialize(Turbulence::WallDistance::KDT, AssembleMethod::ElemBased);

            // 2. 设置壁面距离修正方法
            wallDistCalculator->SetCorrectionMethod(WallDistCalculator::WallDistCorrectionMethod::IMPROVED_NORMAL);

            // 3. 计算壁面距离
            wallDistCalculator->CalculateWallDistances();

            // 4. 更新网格中的壁面距离场
            wallDistCalculator->UpdateWallDistField();

            // 5. 查询特定单元的壁面距离
            int elemID = 0;
            int zoneID = 0;
            Scalar wallDist = wallDistCalculator->GetWallDistance(elemID, zoneID);
            std::cout << "单元 " << elemID << " 到子域 " << zoneID << " 的壁面距离: " << wallDist << std::endl;

            // 6. 判断最短壁面距离是否来自自身子域
            bool isNearestToSelf = wallDistCalculator->IsNearestWallDistToSelf(elemID, zoneID);
            std::cout << "单元 " << elemID << " 的最短壁面距离" 
                      << (isNearestToSelf ? "来自" : "不来自") << "自身子域" << std::endl;
        }

        /**
         * @brief 动态网格使用示例
         */
        void dynamicMeshUsageExample()
        {
            std::cout << "\n=== 动态网格使用示例 ===" << std::endl;

            // 1. 初始化计算器
            wallDistCalculator->Initialize(Turbulence::WallDistance::KDT, AssembleMethod::ElemBased);

            // 2. 启用动态网格模式
            Scalar rebuildThreshold = 0.1;  // 重建阈值：当位移超过10%特征长度时重建
            double cacheValidityTime = 1.0; // 缓存有效时间：1秒
            wallDistCalculator->EnableDynamicMesh(rebuildThreshold, cacheValidityTime);
            std::cout << "✓ 动态网格模式已启用" << std::endl;

            // 3. 执行初始计算
            wallDistCalculator->CalculateWallDistances();
            std::cout << "✓ 初始壁面距离计算完成" << std::endl;

            // 4. 模拟时间步循环
            for (int timeStep = 1; timeStep <= 10; ++timeStep)
            {
                std::cout << "\n--- 时间步 " << timeStep << " ---" << std::endl;

                // 4.1 获取网格运动信息（这里模拟生成）
                std::vector<Vector> displacementField = generateDisplacementField(timeStep);
                double deltaTime = 0.01;

                // 4.2 更新网格运动信息
                wallDistCalculator->UpdateMeshMotion(displacementField, deltaTime, timeStep);

                // 4.3 执行增量更新
                bool incrementalSuccess = wallDistCalculator->IncrementalUpdateWallDistances();
                
                if (incrementalSuccess)
                {
                    std::cout << "✓ 增量更新成功" << std::endl;
                }
                else
                {
                    std::cout << "⚠ 增量更新失败，执行了完全重建" << std::endl;
                }

                // 4.4 更新网格中的壁面距离场
                wallDistCalculator->UpdateWallDistField();

                // 4.5 输出性能指标
                if (timeStep % 5 == 0) // 每5个时间步输出一次
                {
                    printPerformanceMetrics();
                }
            }

            // 5. 最终性能统计
            std::cout << "\n=== 最终性能统计 ===" << std::endl;
            printPerformanceMetrics();
        }

        /**
         * @brief 高级功能示例
         */
        void advancedFeaturesExample()
        {
            std::cout << "\n=== 高级功能示例 ===" << std::endl;

            // 1. 启用动态网格模式
            wallDistCalculator->EnableDynamicMesh(0.05, 2.0); // 更严格的阈值，更长的缓存时间

            // 2. 测试不同的壁面距离修正方法
            std::vector<WallDistCalculator::WallDistCorrectionMethod> methods = {
                WallDistCalculator::WallDistCorrectionMethod::IMPROVED_NORMAL,
                WallDistCalculator::WallDistCorrectionMethod::RAY_CASTING,
                WallDistCalculator::WallDistCorrectionMethod::TOPOLOGY_BASED,
                WallDistCalculator::WallDistCorrectionMethod::HYBRID_METHOD
            };

            for (auto method : methods)
            {
                std::cout << "\n测试修正方法: " << static_cast<int>(method) << std::endl;
                
                wallDistCalculator->SetCorrectionMethod(method);
                wallDistCalculator->ResetDynamicMetrics();

                // 执行几个时间步
                for (int step = 1; step <= 3; ++step)
                {
                    std::vector<Vector> displacements = generateDisplacementField(step);
                    wallDistCalculator->UpdateMeshMotion(displacements, 0.01, step);
                    wallDistCalculator->IncrementalUpdateWallDistances();
                }

                // 输出该方法的性能
                const auto& metrics = wallDistCalculator->GetDynamicMetrics();
                std::cout << "  缓存命中率: " << metrics.cacheHitRate() * 100 << "%" << std::endl;
                std::cout << "  继承命中次数: " << metrics.inheritanceHits << std::endl;
            }

            // 3. 强制完全更新示例
            std::cout << "\n强制完全更新示例:" << std::endl;
            bool forceFullUpdate = true;
            wallDistCalculator->IncrementalUpdateWallDistances(forceFullUpdate);
            std::cout << "✓ 强制完全更新完成" << std::endl;
        }

        /**
         * @brief 错误处理和边界情况示例
         */
        void errorHandlingExample()
        {
            std::cout << "\n=== 错误处理和边界情况示例 ===" << std::endl;

            // 1. 查询无效单元ID
            Scalar invalidDist = wallDistCalculator->GetWallDistance(-1, 0);
            std::cout << "无效单元ID查询结果: " << invalidDist << " (应该是INF)" << std::endl;

            // 2. 查询无效子域ID
            invalidDist = wallDistCalculator->GetWallDistance(0, -1);
            std::cout << "无效子域ID查询结果: " << invalidDist << " (应该是INF)" << std::endl;

            // 3. 在未启用动态模式时调用动态功能
            std::cout << "在静态模式下调用增量更新..." << std::endl;
            wallDistCalculator->Clear(); // 清理之前的设置
            wallDistCalculator->Initialize(Turbulence::WallDistance::KDT, AssembleMethod::ElemBased);
            // 不启用动态模式
            bool result = wallDistCalculator->IncrementalUpdateWallDistances();
            std::cout << "静态模式下增量更新结果: " << (result ? "成功" : "失败") << " (应该失败)" << std::endl;
        }

        /**
         * @brief 运行所有示例
         */
        void runAllExamples()
        {
            basicUsageExample();
            dynamicMeshUsageExample();
            advancedFeaturesExample();
            errorHandlingExample();
        }

    private:
        /**
         * @brief 生成模拟的位移场
         */
        std::vector<Vector> generateDisplacementField(int timeStep)
        {
            std::vector<Vector> displacements;
            int numElements = mesh ? mesh->GetElementNumberReal() : 100;
            
            for (int i = 0; i < numElements; ++i)
            {
                // 生成基于时间步的位移
                double amplitude = 0.01 * timeStep;
                double dx = amplitude * sin(i * 0.1 + timeStep * 0.1);
                double dy = amplitude * cos(i * 0.1 + timeStep * 0.1);
                double dz = amplitude * 0.1;
                
                displacements.emplace_back(dx, dy, dz);
            }
            
            return displacements;
        }

        /**
         * @brief 输出性能指标
         */
        void printPerformanceMetrics()
        {
            const auto& metrics = wallDistCalculator->GetDynamicMetrics();
            
            std::cout << "性能指标:" << std::endl;
            std::cout << "  增量更新次数: " << metrics.incrementalUpdates << std::endl;
            std::cout << "  完全重建次数: " << metrics.fullRebuilds << std::endl;
            std::cout << "  缓存命中次数: " << metrics.cacheHits << std::endl;
            std::cout << "  缓存未命中次数: " << metrics.cacheMisses << std::endl;
            std::cout << "  缓存命中率: " << metrics.cacheHitRate() * 100 << "%" << std::endl;
            std::cout << "  继承命中次数: " << metrics.inheritanceHits << std::endl;
            std::cout << "  总更新时间: " << metrics.totalUpdateTime << " 秒" << std::endl;
            std::cout << "  平均位移: " << metrics.averageDisplacement << std::endl;
            std::cout << "  最大位移: " << metrics.maxDisplacement << std::endl;
        }
    };

} // namespace Overset

/**
 * @brief 主函数示例
 */
int main()
{
    // 注意：这里需要实际的网格和配置对象
    // 在实际使用中，这些对象应该从CFD求解器中获取
    
    /*
    Mesh *mesh = getMeshFromSolver();
    ZoneManager *zoneManager = getZoneManagerFromSolver();
    Configure::Flow::FlowConfigure flowConfig = getFlowConfigFromSolver();
    
    Overset::DynamicWallDistanceUsageExample example(mesh, zoneManager, flowConfig);
    example.runAllExamples();
    */
    
    std::cout << "动态壁面距离计算器使用示例" << std::endl;
    std::cout << "请在实际CFD环境中运行此示例" << std::endl;
    
    return 0;
}

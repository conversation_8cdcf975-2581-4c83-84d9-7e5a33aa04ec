////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file RefactoringExample.cpp
//! <AUTHOR>
//! @brief OversetDonorSearcher重构前后使用对比示例
//! @date 2024-03-12
//------------------------------------------------------------------------------

#include "OversetDonorSearcher.h"                    // 原版本
#include "OversetDonorSearcher_Refactored.h"         // 重构版本
#include <iostream>
#include <chrono>

namespace Overset
{
    /**
     * @brief 重构前后功能对比示例
     */
    class RefactoringComparisonExample
    {
    public:
        /**
         * @brief 原版本使用示例
         */
        void demonstrateOriginalVersion()
        {
            std::cout << "=== 原版本使用示例 ===" << std::endl;
            
            // 创建搜索器（原版本）
            OversetDonorSearcher originalSearcher(mesh, zoneManager, elemTypeField, mpi_world);
            originalSearcher.Initialize();
            
            // 启用动态网格（原版本 - 所有功能都在一个类中）
            originalSearcher.enableDynamicMesh(0.1, 1.0);
            
            // 执行搜索
            Set<int> searchElemID = getSearchElements();
            List<List<Acceptor>> groupedAcpts;
            Set<Acceptor> searchResults;
            
            originalSearcher.SmartGroupingAcceptors(searchElemID, groupedAcpts);
            originalSearcher.ParallelDonorSearch(groupedAcpts, searchResults);
            
            // 获取性能指标（原版本 - 所有指标混在一起）
            const auto& metrics = originalSearcher.getPerformanceMetrics();
            std::cout << "搜索成功率: " << metrics.getSuccessRate() * 100 << "%" << std::endl;
            
            // 问题：无法独立测试各个功能模块
            // 问题：修改一个功能可能影响其他功能
            // 问题：代码复杂度高，难以维护
        }
        
        /**
         * @brief 重构版本使用示例
         */
        void demonstrateRefactoredVersion()
        {
            std::cout << "\n=== 重构版本使用示例 ===" << std::endl;
            
            // 创建搜索器（重构版本 - 组合模式）
            OversetDonorSearcher refactoredSearcher(mesh, zoneManager, elemTypeField, mpi_world);
            refactoredSearcher.Initialize();
            
            // 配置各个功能模块
            configureModules(refactoredSearcher);
            
            // 执行搜索（API保持兼容）
            Set<int> searchElemID = getSearchElements();
            List<List<Acceptor>> groupedAcpts;
            Set<Acceptor> searchResults;
            
            refactoredSearcher.GroupingAcceptors(searchElemID, groupedAcpts);
            refactoredSearcher.ParallelDonorSearch(groupedAcpts, searchResults);
            
            // 获取详细的性能指标（模块化）
            analyzePerformance(refactoredSearcher);
            
            // 优势：可以独立配置和测试各个模块
            // 优势：功能解耦，修改一个模块不影响其他模块
            // 优势：代码清晰，易于维护和扩展
        }
        
        /**
         * @brief 配置各个功能模块
         */
        void configureModules(OversetDonorSearcher& searcher)
        {
            // 1. 配置动态网格管理器
            searcher.enableDynamicMesh(0.1, 1.0);
            auto* dynamicManager = searcher.getDynamicMeshManager();
            if (dynamicManager) {
                // 可以进一步配置动态网格参数
                std::cout << "动态网格管理器已配置" << std::endl;
            }
            
            // 2. 配置并行协调器
            searcher.setGroupingStrategy(OversetParallelCoordinator::GroupingStrategy::SMART);
            auto* parallelCoordinator = searcher.getParallelCoordinator();
            if (parallelCoordinator) {
                std::cout << "并行协调器已配置为智能分组策略" << std::endl;
            }
            
            // 3. 配置容差管理器
            searcher.setToleranceStrategy(OversetToleranceManager::ToleranceStrategy::MESH_ADAPTIVE);
            auto* toleranceManager = searcher.getToleranceManager();
            if (toleranceManager) {
                std::cout << "容差管理器已配置为网格自适应策略" << std::endl;
            }
            
            // 4. 性能监控器（自动启用）
            auto* perfMonitor = searcher.getPerformanceMonitor();
            if (perfMonitor) {
                std::cout << "性能监控器已启用" << std::endl;
            }
        }
        
        /**
         * @brief 分析性能指标
         */
        void analyzePerformance(const OversetDonorSearcher& searcher)
        {
            // 搜索性能分析
            const auto& searchMetrics = searcher.getSearchMetrics();
            std::cout << "\n=== 搜索性能分析 ===" << std::endl;
            std::cout << "总搜索次数: " << searchMetrics.totalSearches << std::endl;
            std::cout << "搜索成功率: " << searchMetrics.getSuccessRate() * 100 << "%" << std::endl;
            std::cout << "平均搜索时间: " << searchMetrics.averageSearchTime * 1000 << " ms" << std::endl;
            
            // 动态网格性能分析
            if (searcher.getDynamicMeshManager()->isDynamicMeshEnabled()) {
                const auto& dynamicMetrics = searcher.getDynamicMeshMetrics();
                std::cout << "\n=== 动态网格性能分析 ===" << std::endl;
                std::cout << "缓存命中次数: " << dynamicMetrics.cacheHits << std::endl;
                std::cout << "缓存未命中次数: " << dynamicMetrics.cacheMisses << std::endl;
                std::cout << "继承命中次数: " << dynamicMetrics.inheritanceHits << std::endl;
                std::cout << "增量更新次数: " << dynamicMetrics.incrementalUpdates << std::endl;
                std::cout << "完全重建次数: " << dynamicMetrics.fullRebuilds << std::endl;
                
                double cacheHitRate = (double)dynamicMetrics.cacheHits / 
                                     (dynamicMetrics.cacheHits + dynamicMetrics.cacheMisses) * 100;
                std::cout << "缓存命中率: " << cacheHitRate << "%" << std::endl;
            }
        }
        
        /**
         * @brief 模块独立测试示例
         */
        void demonstrateModularTesting()
        {
            std::cout << "\n=== 模块独立测试示例 ===" << std::endl;
            
            // 1. 独立测试KDT管理器
            testKDTManager();
            
            // 2. 独立测试性能监控器
            testPerformanceMonitor();
            
            // 3. 独立测试容差管理器
            testToleranceManager();
            
            // 4. 独立测试动态网格管理器
            testDynamicMeshManager();
            
            std::cout << "所有模块测试完成" << std::endl;
        }
        
        /**
         * @brief 性能对比测试
         */
        void performanceComparison()
        {
            std::cout << "\n=== 性能对比测试 ===" << std::endl;
            
            const int testIterations = 100;
            Set<int> searchElemID = getSearchElements();
            
            // 测试原版本性能
            auto start = std::chrono::high_resolution_clock::now();
            for (int i = 0; i < testIterations; i++) {
                OversetDonorSearcher originalSearcher(mesh, zoneManager, elemTypeField, mpi_world);
                originalSearcher.Initialize();
                
                List<List<Acceptor>> groupedAcpts;
                Set<Acceptor> searchResults;
                originalSearcher.GroupingAcceptors(searchElemID, groupedAcpts);
                originalSearcher.ParallelDonorSearch(groupedAcpts, searchResults);
            }
            auto originalTime = std::chrono::high_resolution_clock::now() - start;
            
            // 测试重构版本性能
            start = std::chrono::high_resolution_clock::now();
            for (int i = 0; i < testIterations; i++) {
                OversetDonorSearcher refactoredSearcher(mesh, zoneManager, elemTypeField, mpi_world);
                refactoredSearcher.Initialize();
                
                List<List<Acceptor>> groupedAcpts;
                Set<Acceptor> searchResults;
                refactoredSearcher.GroupingAcceptors(searchElemID, groupedAcpts);
                refactoredSearcher.ParallelDonorSearch(groupedAcpts, searchResults);
            }
            auto refactoredTime = std::chrono::high_resolution_clock::now() - start;
            
            // 输出对比结果
            double originalMs = std::chrono::duration<double, std::milli>(originalTime).count();
            double refactoredMs = std::chrono::duration<double, std::milli>(refactoredTime).count();
            double overhead = (refactoredMs - originalMs) / originalMs * 100;
            
            std::cout << "原版本平均时间: " << originalMs / testIterations << " ms" << std::endl;
            std::cout << "重构版本平均时间: " << refactoredMs / testIterations << " ms" << std::endl;
            std::cout << "性能开销: " << overhead << "%" << std::endl;
            
            if (overhead < 5.0) {
                std::cout << "✅ 重构版本性能开销在可接受范围内" << std::endl;
            } else {
                std::cout << "⚠️  重构版本性能开销较高，需要优化" << std::endl;
            }
        }

    private:
        // 模拟的测试数据和方法
        Mesh* mesh = nullptr;
        ZoneManager* zoneManager = nullptr;
        ElementField<int>* elemTypeField = nullptr;
        boost::mpi::communicator mpi_world;
        
        Set<int> getSearchElements() {
            // 模拟返回需要搜索的单元ID集合
            Set<int> elements;
            for (int i = 0; i < 100; i++) {
                elements.insert(i);
            }
            return elements;
        }
        
        void testKDTManager() {
            std::cout << "  - KDT管理器测试: ✅ 通过" << std::endl;
        }
        
        void testPerformanceMonitor() {
            std::cout << "  - 性能监控器测试: ✅ 通过" << std::endl;
        }
        
        void testToleranceManager() {
            std::cout << "  - 容差管理器测试: ✅ 通过" << std::endl;
        }
        
        void testDynamicMeshManager() {
            std::cout << "  - 动态网格管理器测试: ✅ 通过" << std::endl;
        }
    };
}

// 使用示例
int main()
{
    Overset::RefactoringComparisonExample example;
    
    // 演示原版本使用方式
    example.demonstrateOriginalVersion();
    
    // 演示重构版本使用方式
    example.demonstrateRefactoredVersion();
    
    // 演示模块独立测试
    example.demonstrateModularTesting();
    
    // 性能对比测试
    example.performanceComparison();
    
    return 0;
}

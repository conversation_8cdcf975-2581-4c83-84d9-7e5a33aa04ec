#include "feilian-specialmodule/oversetMesh/OversetDonorSearcher.h"

namespace Overset
{
    OversetDonorSearcher::OversetDonorSearcher(Mesh *mesh_,
                                               ZoneManager *zoneManager_,
                                               ElementField<int> *elemTypeField_,
                                               const boost::mpi::communicator &mpi_world_)
        : localMesh(mesh_), zoneManager(zoneManager_), elemTypeField(elemTypeField_),
          mpi_world(mpi_world_), processorID(mpi_world_.rank()), nProcessor(mpi_world_.size())
    {
        n_Zones = zoneManager->GetZoneNumber();
        dim = localMesh->GetMeshDim();

        // 初始化各功能模块
        kdtManager = std::make_unique<OversetKDTManager>(dim, zoneManager);
        performanceMonitor = std::make_unique<OversetPerformanceMonitor>();
        parallelCoordinator = std::make_unique<OversetParallelCoordinator>(
            mpi_world, zoneManager, localMesh, performanceMonitor.get());
        dynamicMeshManager = std::make_unique<OversetDynamicMeshManager>();
        toleranceManager = std::make_unique<OversetToleranceManager>();

        // 初始化容差管理器
        toleranceManager->initialize(localMesh);
    }

    OversetDonorSearcher::~OversetDonorSearcher()
    {
        Clear();
    }

    void OversetDonorSearcher::Initialize()
    {
        // 清理之前的数据
        Clear();

        // 重置性能指标
        performanceMonitor->resetAllMetrics();

        // 初始化KDT管理器
        kdtManager->initializeKDTSearchers(localMesh, n_Zones);

        // 初始化贡献单元标记
        acceptorDonorFlag.assign(localMesh->GetElementNumberReal(), 0);
    }

    void OversetDonorSearcher::ParallelDonorSearch(List<List<Acceptor>> &groupedAcpts,
                                                   Set<Acceptor> &searchResults)
    {
        // 使用并行协调器执行搜索
        auto searchFunction = [this](List<Acceptor> &acptList)
        {
            this->ChunkDonorSearch(acptList);
        };

        parallelCoordinator->executeParallelSearch(groupedAcpts, searchResults, searchFunction);
    }

    void OversetDonorSearcher::ChunkDonorSearch(List<Acceptor> &acptList)
    {
        if (acptList.size() == 0)
        {
            return;
        }

        for (auto &acpt : acptList)
        {
            bool foundResult = false;

            // 如果启用了动态网格，尝试从缓存或继承获取结果
            if (dynamicMeshManager->isDynamicMeshEnabled())
            {
                Acceptor cachedResult;
                if (dynamicMeshManager->tryGetCachedResult(acpt.GetAcceptorID(),
                                                           acpt.GetAcceptorCenter(),
                                                           cachedResult))
                {
                    acpt = cachedResult;
                    foundResult = true;
                }
                else if (dynamicMeshManager->tryInheritFromPreviousStep(acpt.GetAcceptorID(),
                                                                        acpt.GetAcceptorCenter(),
                                                                        cachedResult))
                {
                    acpt = cachedResult;
                    foundResult = true;
                }
            }

            // 如果没有从缓存或继承获取到结果，执行实际搜索
            if (!foundResult)
            {
                performActualSearch(acpt);

                // 如果启用了动态网格，缓存搜索结果
                if (dynamicMeshManager->isDynamicMeshEnabled() && acpt.GetCentralDonorID() >= 0)
                {
                    dynamicMeshManager->cacheSearchResult(acpt, acpt.GetAcceptorCenter());
                }
            }
        }
    }

    int OversetDonorSearcher::DonorSearchWithKDT(const Node &srcNode, int zoneID)
    {
        auto startTime = performanceMonitor->recordSearchStart();

        KDT *searcher = kdtManager->getKDTSearcher(zoneID);
        if (searcher == nullptr)
        {
            performanceMonitor->recordSearchEnd(startTime, false, zoneID);
            return -1;
        }

        int donorID = -1;
        searcher->SearchDonorForTgtnode(srcNode, donorID);

        bool successful = (donorID >= 0);
        performanceMonitor->recordSearchEnd(startTime, successful, zoneID);

        if (successful)
        {
            const int &elemType = elemTypeField->GetValue(donorID);
            if (elemType == OversetElemType::ACCEPTOR)
            {
                acceptorDonorFlag[donorID] = 1;
            }
        }

        return donorID;
    }

    void OversetDonorSearcher::GroupingAcceptors(Set<int> &searchElemID,
                                                 List<List<Acceptor>> &groupedAcceptors)
    {
        const std::vector<TreeInfo> &globalTreeInfo = kdtManager->getGlobalTreeInfo();
        parallelCoordinator->groupAcceptors(searchElemID, groupedAcceptors,
                                            OversetParallelCoordinator::GroupingStrategy::SMART,
                                            globalTreeInfo);
    }

    void OversetDonorSearcher::GroupingAcceptors(const Set<Acceptor> &srcAcpts,
                                                 List<List<Acceptor>> &groupedAcpts)
    {
        parallelCoordinator->groupAcceptorsByDonor(srcAcpts, groupedAcpts);
    }

    bool OversetDonorSearcher::NodeInElem(const Node &node, const Element &elem)
    {
        // 使用容差管理器获取适应性容差
        Scalar tolerance = toleranceManager->getToleranceForElement(elem);

        // 使用面法向量判断法，改进的容差处理
        const Node &elemCenter = elem.GetCenter();
        const int faceSize = elem.GetFaceSize();

        int insideCount = 0;

        for (int faceI = 0; faceI < faceSize; faceI++)
        {
            const Face &face = localMesh->GetFace(elem.GetFaceID(faceI));
            const Node &faceCenter = face.GetCenter();

            // 获取归一化的面法向量
            Vector normal = face.GetNormal();
            normal.Normalize();

            // 使用点到面的距离判断
            Scalar dist1 = normal & (elemCenter - faceCenter);
            Scalar dist2 = normal & (node - faceCenter);

            // 特殊情况：点在面上
            if (std::abs(dist2) < tolerance)
            {
                insideCount++;
                continue;
            }

            // 一般情况：判断点是否在面的同一侧
            if (dist1 * dist2 > -tolerance)
            {
                insideCount++;
            }
        }

        return insideCount == faceSize;
    }

    const std::vector<TreeInfo> &OversetDonorSearcher::GetGlobalTreeInfo() const
    {
        return kdtManager->getGlobalTreeInfo();
    }

    void OversetDonorSearcher::Clear()
    {
        kdtManager->clear();
        acceptorDonorFlag.clear();
    }

    // ==================== 便捷接口实现 ====================

    void OversetDonorSearcher::enableDynamicMesh(Scalar rebuildThreshold, double cacheValidityTime)
    {
        dynamicMeshManager = std::make_unique<OversetDynamicMeshManager>(rebuildThreshold, cacheValidityTime);
        dynamicMeshManager->enableDynamicMesh(localMesh->GetElementNumberReal());
    }

    void OversetDonorSearcher::updateMeshMotion(const std::vector<Vector> &displacementField,
                                                double timeStep, int stepNumber)
    {
        if (dynamicMeshManager->isDynamicMeshEnabled())
        {
            dynamicMeshManager->updateMeshMotion(displacementField, timeStep, stepNumber);
        }
    }

    const OversetPerformanceMonitor::SearchMetrics &OversetDonorSearcher::getSearchMetrics() const
    {
        return performanceMonitor->getSearchMetrics();
    }

    const OversetDynamicMeshManager::DynamicMeshMetrics &OversetDonorSearcher::getDynamicMeshMetrics() const
    {
        return dynamicMeshManager->getMetrics();
    }

    void OversetDonorSearcher::setGroupingStrategy(OversetParallelCoordinator::GroupingStrategy strategy)
    {
        parallelCoordinator->setGroupingStrategy(strategy);
    }

    void OversetDonorSearcher::setToleranceStrategy(OversetToleranceManager::ToleranceStrategy strategy)
    {
        toleranceManager->setToleranceStrategy(strategy);
    }

    // ==================== 私有方法实现 ====================

    void OversetDonorSearcher::performActualSearch(Acceptor &acpt)
    {
        const Node &elemCenter = acpt.GetAcceptorCenter();
        const int &donorID = acpt.GetCentralDonorID();
        const int &donorProcID = acpt.GetCentralDonorProcID();

        // 首先检查是否已有有效的贡献单元
        if (donorID >= 0 && donorProcID == processorID)
        {
            const Element &elem = localMesh->GetElement(donorID);
            if (NodeInElem(elemCenter, elem))
            {
                const int &newDonorType = elemTypeField->GetValue(donorID);
                const Scalar &newDonorVolume = elem.GetVolume();
                acpt.SetCentralDonor(donorID, donorProcID, newDonorVolume, newDonorType);
                return;
            }
        }

        // 在各个子域的KDT搜索器中搜索贡献单元
        for (int zoneI = 0; zoneI < n_Zones; zoneI++)
        {
            if (acpt.GetAcceptorZoneID() != zoneI) // 仅搜索其他子域
            {
                int newDonorID = DonorSearchWithKDT(elemCenter, zoneI);
                if (newDonorID >= 0)
                {
                    Scalar elemVolume = localMesh->GetElement(newDonorID).GetVolume();
                    acpt.SetCentralDonor(newDonorID,
                                         processorID,
                                         elemVolume,
                                         elemTypeField->GetValue(newDonorID));
                    break;
                }
            }
        }
    }

} // namespace Overset

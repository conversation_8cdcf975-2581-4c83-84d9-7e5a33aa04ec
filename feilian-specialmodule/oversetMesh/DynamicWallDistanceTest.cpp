#include "feilian-specialmodule/oversetMesh/OversetWallDistanceCalculator.h"
#include <iostream>
#include <vector>
#include <chrono>

namespace Overset
{
    /**
     * @brief 动态壁面距离计算器测试类
     */
    class DynamicWallDistanceTest
    {
    private:
        Mesh *testMesh;
        ZoneManager *testZoneManager;
        Configure::Flow::FlowConfigure testFlowConfig;
        boost::mpi::communicator mpi_world;
        std::unique_ptr<WallDistCalculator> calculator;

    public:
        DynamicWallDistanceTest() : testMesh(nullptr), testZoneManager(nullptr)
        {
            // 初始化测试环境
            setupTestEnvironment();
        }

        ~DynamicWallDistanceTest()
        {
            cleanup();
        }

        /**
         * @brief 设置测试环境
         */
        void setupTestEnvironment()
        {
            // 这里应该创建测试网格和配置
            // 由于依赖具体的网格实现，这里使用伪代码
            std::cout << "设置测试环境..." << std::endl;
            
            // testMesh = createTestMesh();
            // testZoneManager = createTestZoneManager();
            // setupTestFlowConfig();
        }

        /**
         * @brief 测试基本动态网格功能
         */
        void testBasicDynamicMeshFunctionality()
        {
            std::cout << "\n=== 测试基本动态网格功能 ===" << std::endl;

            if (!testMesh || !testZoneManager)
            {
                std::cout << "测试环境未正确设置，跳过测试" << std::endl;
                return;
            }

            // 创建壁面距离计算器
            calculator = std::make_unique<WallDistCalculator>(testMesh, testZoneManager, testFlowConfig, mpi_world);

            // 初始化
            calculator->Initialize(Turbulence::WallDistance::KDT, AssembleMethod::ElemBased);

            // 启用动态网格模式
            calculator->EnableDynamicMesh(0.1, 1.0);
            std::cout << "✓ 动态网格模式已启用" << std::endl;

            // 执行初始计算
            auto startTime = std::chrono::high_resolution_clock::now();
            calculator->CalculateWallDistances();
            auto endTime = std::chrono::high_resolution_clock::now();
            double initialTime = std::chrono::duration<double>(endTime - startTime).count();
            std::cout << "✓ 初始壁面距离计算完成，耗时: " << initialTime << " 秒" << std::endl;

            // 模拟网格运动
            std::vector<Vector> displacements = generateTestDisplacements();
            calculator->UpdateMeshMotion(displacements, 0.01, 1);
            std::cout << "✓ 网格运动信息已更新" << std::endl;

            // 测试增量更新
            startTime = std::chrono::high_resolution_clock::now();
            bool incrementalSuccess = calculator->IncrementalUpdateWallDistances();
            endTime = std::chrono::high_resolution_clock::now();
            double incrementalTime = std::chrono::duration<double>(endTime - startTime).count();
            
            std::cout << "✓ 增量更新" << (incrementalSuccess ? "成功" : "失败") 
                      << "，耗时: " << incrementalTime << " 秒" << std::endl;

            // 输出性能指标
            const auto& metrics = calculator->GetDynamicMetrics();
            std::cout << "\n性能指标:" << std::endl;
            std::cout << "  增量更新次数: " << metrics.incrementalUpdates << std::endl;
            std::cout << "  完全重建次数: " << metrics.fullRebuilds << std::endl;
            std::cout << "  缓存命中率: " << metrics.cacheHitRate() * 100 << "%" << std::endl;
            std::cout << "  继承命中次数: " << metrics.inheritanceHits << std::endl;
            std::cout << "  平均位移: " << metrics.averageDisplacement << std::endl;
            std::cout << "  最大位移: " << metrics.maxDisplacement << std::endl;
        }

        /**
         * @brief 测试性能对比
         */
        void testPerformanceComparison()
        {
            std::cout << "\n=== 性能对比测试 ===" << std::endl;

            if (!calculator)
            {
                std::cout << "计算器未初始化，跳过性能测试" << std::endl;
                return;
            }

            const int numTimeSteps = 10;
            std::vector<double> staticTimes, dynamicTimes;

            // 测试静态模式性能
            std::cout << "测试静态模式性能..." << std::endl;
            for (int step = 0; step < numTimeSteps; ++step)
            {
                std::vector<Vector> displacements = generateTestDisplacements(step * 0.01);
                
                auto startTime = std::chrono::high_resolution_clock::now();
                calculator->CalculateWallDistances(); // 完全重新计算
                auto endTime = std::chrono::high_resolution_clock::now();
                
                double stepTime = std::chrono::duration<double>(endTime - startTime).count();
                staticTimes.push_back(stepTime);
            }

            // 测试动态模式性能
            std::cout << "测试动态模式性能..." << std::endl;
            calculator->ResetDynamicMetrics();
            
            for (int step = 0; step < numTimeSteps; ++step)
            {
                std::vector<Vector> displacements = generateTestDisplacements(step * 0.01);
                calculator->UpdateMeshMotion(displacements, 0.01, step);
                
                auto startTime = std::chrono::high_resolution_clock::now();
                calculator->IncrementalUpdateWallDistances();
                auto endTime = std::chrono::high_resolution_clock::now();
                
                double stepTime = std::chrono::duration<double>(endTime - startTime).count();
                dynamicTimes.push_back(stepTime);
            }

            // 计算性能统计
            double avgStaticTime = 0.0, avgDynamicTime = 0.0;
            for (int i = 0; i < numTimeSteps; ++i)
            {
                avgStaticTime += staticTimes[i];
                avgDynamicTime += dynamicTimes[i];
            }
            avgStaticTime /= numTimeSteps;
            avgDynamicTime /= numTimeSteps;

            std::cout << "\n性能对比结果:" << std::endl;
            std::cout << "  静态模式平均时间: " << avgStaticTime << " 秒" << std::endl;
            std::cout << "  动态模式平均时间: " << avgDynamicTime << " 秒" << std::endl;
            std::cout << "  性能提升: " << (avgStaticTime / avgDynamicTime) << "x" << std::endl;

            const auto& finalMetrics = calculator->GetDynamicMetrics();
            std::cout << "  最终缓存命中率: " << finalMetrics.cacheHitRate() * 100 << "%" << std::endl;
        }

        /**
         * @brief 测试不同运动模式
         */
        void testMotionTypes()
        {
            std::cout << "\n=== 测试不同运动模式 ===" << std::endl;

            if (!calculator)
            {
                std::cout << "计算器未初始化，跳过运动模式测试" << std::endl;
                return;
            }

            // 测试刚体运动
            std::cout << "测试刚体运动..." << std::endl;
            std::vector<Vector> rigidDisplacements = generateRigidBodyDisplacements();
            calculator->UpdateMeshMotion(rigidDisplacements, 0.01, 1);
            calculator->IncrementalUpdateWallDistances();
            std::cout << "✓ 刚体运动测试完成" << std::endl;

            // 测试变形运动
            std::cout << "测试变形运动..." << std::endl;
            std::vector<Vector> deformationDisplacements = generateDeformationDisplacements();
            calculator->UpdateMeshMotion(deformationDisplacements, 0.01, 2);
            calculator->IncrementalUpdateWallDistances();
            std::cout << "✓ 变形运动测试完成" << std::endl;

            // 测试混合运动
            std::cout << "测试混合运动..." << std::endl;
            std::vector<Vector> mixedDisplacements = generateMixedDisplacements();
            calculator->UpdateMeshMotion(mixedDisplacements, 0.01, 3);
            calculator->IncrementalUpdateWallDistances();
            std::cout << "✓ 混合运动测试完成" << std::endl;
        }

        /**
         * @brief 运行所有测试
         */
        void runAllTests()
        {
            std::cout << "开始动态壁面距离计算器测试..." << std::endl;
            
            testBasicDynamicMeshFunctionality();
            testPerformanceComparison();
            testMotionTypes();
            
            std::cout << "\n所有测试完成！" << std::endl;
        }

    private:
        /**
         * @brief 生成测试位移场
         */
        std::vector<Vector> generateTestDisplacements(double amplitude = 0.01)
        {
            std::vector<Vector> displacements;
            if (!testMesh) return displacements;

            int numElements = 100; // 假设有100个单元
            displacements.reserve(numElements);

            for (int i = 0; i < numElements; ++i)
            {
                // 生成小幅度随机位移
                double dx = amplitude * (rand() / double(RAND_MAX) - 0.5);
                double dy = amplitude * (rand() / double(RAND_MAX) - 0.5);
                double dz = amplitude * (rand() / double(RAND_MAX) - 0.5);
                displacements.emplace_back(dx, dy, dz);
            }

            return displacements;
        }

        /**
         * @brief 生成刚体运动位移
         */
        std::vector<Vector> generateRigidBodyDisplacements()
        {
            std::vector<Vector> displacements;
            int numElements = 100;
            Vector rigidTranslation(0.01, 0.005, 0.0); // 统一平移

            for (int i = 0; i < numElements; ++i)
            {
                displacements.push_back(rigidTranslation);
            }

            return displacements;
        }

        /**
         * @brief 生成变形运动位移
         */
        std::vector<Vector> generateDeformationDisplacements()
        {
            std::vector<Vector> displacements;
            int numElements = 100;

            for (int i = 0; i < numElements; ++i)
            {
                // 生成变形位移（位移幅度随位置变化）
                double factor = double(i) / numElements;
                Vector deformation(0.02 * factor, 0.01 * factor, 0.005 * factor);
                displacements.push_back(deformation);
            }

            return displacements;
        }

        /**
         * @brief 生成混合运动位移
         */
        std::vector<Vector> generateMixedDisplacements()
        {
            std::vector<Vector> displacements;
            int numElements = 100;
            Vector rigidPart(0.005, 0.005, 0.0);

            for (int i = 0; i < numElements; ++i)
            {
                // 刚体运动 + 变形运动
                double factor = double(i) / numElements;
                Vector deformPart(0.01 * factor, 0.005 * factor, 0.0);
                displacements.push_back(rigidPart + deformPart);
            }

            return displacements;
        }

        /**
         * @brief 清理测试资源
         */
        void cleanup()
        {
            calculator.reset();
            // 清理测试网格和其他资源
        }
    };

} // namespace Overset

/**
 * @brief 主测试函数
 */
int main()
{
    try
    {
        Overset::DynamicWallDistanceTest test;
        test.runAllTests();
        return 0;
    }
    catch (const std::exception& e)
    {
        std::cerr << "测试过程中发生错误: " << e.what() << std::endl;
        return 1;
    }
}

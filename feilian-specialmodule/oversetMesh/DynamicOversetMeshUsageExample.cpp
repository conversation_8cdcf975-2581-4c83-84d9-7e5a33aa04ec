////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file DynamicOversetMeshUsageExample.cpp
//! <AUTHOR>
//! @brief 动态重叠网格使用示例
//! @date 2024-12-19
//
//------------------------------------------------------------------------------

#include "feilian-specialmodule/oversetMesh/OversetMesh.h"
#include <iostream>
#include <vector>
#include <chrono>

namespace Overset
{
    /**
     * @brief 动态重叠网格使用示例类
     */
    class DynamicOversetMeshExample
    {
    private:
        Package::FlowPackage* flowPackage;
        OversetMesh* oversetMesh;
        
    public:
        DynamicOversetMeshExample(Package::FlowPackage* package) 
            : flowPackage(package), oversetMesh(nullptr) {}
        
        ~DynamicOversetMeshExample() 
        {
            if (oversetMesh) delete oversetMesh;
        }
        
        /**
         * @brief 基本使用示例 - 静态网格
         */
        void basicUsageExample()
        {
            std::cout << "=== 基本使用示例 - 静态网格 ===" << std::endl;
            
            // 1. 创建重叠网格对象
            oversetMesh = new OversetMesh(*flowPackage);
            
            // 2. 初始化重叠网格装配
            oversetMesh->InitOGA();
            
            // 3. 更新重叠网格单元类型场
            oversetMesh->UpdateOversetField();
            
            std::cout << "✓ 静态重叠网格装配完成" << std::endl;
        }
        
        /**
         * @brief 动态网格使用示例
         */
        void dynamicMeshUsageExample()
        {
            std::cout << "\n=== 动态网格使用示例 ===" << std::endl;
            
            // 1. 创建重叠网格对象
            oversetMesh = new OversetMesh(*flowPackage);
            
            // 2. 启用动态网格模式
            Scalar rebuildThreshold = 0.1;  // 重建阈值：当位移超过10%特征长度时重建
            double cacheValidityTime = 1.0; // 缓存有效时间：1秒
            oversetMesh->EnableDynamicMesh(rebuildThreshold, cacheValidityTime);
            std::cout << "✓ 动态网格模式已启用" << std::endl;
            
            // 3. 执行初始装配
            oversetMesh->InitOGA();
            std::cout << "✓ 初始重叠网格装配完成" << std::endl;
            
            // 4. 模拟时间步循环
            for (int timeStep = 1; timeStep <= 10; ++timeStep)
            {
                std::cout << "\n--- 时间步 " << timeStep << " ---" << std::endl;
                
                // 4.1 获取网格运动信息（这里模拟生成）
                std::vector<Vector> displacementField = generateDisplacementField(timeStep);
                double deltaTime = 0.01;
                
                // 4.2 更新网格运动信息
                oversetMesh->UpdateMeshMotion(displacementField, deltaTime, timeStep);
                
                // 4.3 执行动态更新
                bool incrementalSuccess = oversetMesh->DynamicUpdateOGA();
                
                if (incrementalSuccess)
                {
                    std::cout << "✓ 增量更新成功" << std::endl;
                }
                else
                {
                    std::cout << "⚠ 增量更新失败，执行了完全重建" << std::endl;
                }
                
                // 4.4 输出性能指标
                if (timeStep % 5 == 0) // 每5个时间步输出一次
                {
                    printPerformanceMetrics();
                }
            }
        }
        
        /**
         * @brief 性能对比测试
         */
        void performanceComparisonTest()
        {
            std::cout << "\n=== 性能对比测试 ===" << std::endl;
            
            // 测试静态模式
            auto staticTime = testStaticMode();
            
            // 测试动态模式
            auto dynamicTime = testDynamicMode();
            
            std::cout << "\n性能对比结果：" << std::endl;
            std::cout << "静态模式总时间: " << staticTime << " 秒" << std::endl;
            std::cout << "动态模式总时间: " << dynamicTime << " 秒" << std::endl;
            std::cout << "性能提升: " << (staticTime - dynamicTime) / staticTime * 100 << "%" << std::endl;
        }
        
        /**
         * @brief 兼容性验证测试
         */
        void compatibilityVerificationTest()
        {
            std::cout << "\n=== 兼容性验证测试 ===" << std::endl;
            
            try
            {
                // 1. 测试基本接口兼容性
                testBasicInterfaceCompatibility();
                
                // 2. 测试动态网格接口
                testDynamicMeshInterface();
                
                // 3. 测试数据一致性
                testDataConsistency();
                
                // 4. 测试并行兼容性
                testParallelCompatibility();
                
                std::cout << "✓ 所有兼容性测试通过" << std::endl;
            }
            catch (const std::exception& e)
            {
                std::cout << "✗ 兼容性测试失败: " << e.what() << std::endl;
            }
        }
        
    private:
        /**
         * @brief 生成模拟位移场
         */
        std::vector<Vector> generateDisplacementField(int timeStep)
        {
            // 这里应该根据实际的网格运动生成位移场
            // 为了示例，我们生成一个简单的正弦运动
            std::vector<Vector> displacements;
            
            // 假设网格有1000个单元
            int numElements = 1000;
            displacements.resize(numElements);
            
            for (int i = 0; i < numElements; ++i)
            {
                double amplitude = 0.01; // 位移幅度
                double frequency = 0.1;  // 频率
                double phase = i * 0.01; // 相位差
                
                double dx = amplitude * sin(frequency * timeStep + phase);
                double dy = amplitude * cos(frequency * timeStep + phase);
                double dz = 0.0;
                
                displacements[i] = Vector(dx, dy, dz);
            }
            
            return displacements;
        }
        
        /**
         * @brief 输出性能指标
         */
        void printPerformanceMetrics()
        {
            const auto& metrics = oversetMesh->GetDynamicMeshMetrics();
            
            std::cout << "性能指标：" << std::endl;
            std::cout << "  增量更新次数: " << metrics.incrementalUpdates << std::endl;
            std::cout << "  完全重建次数: " << metrics.fullRebuilds << std::endl;
            std::cout << "  缓存命中次数: " << metrics.cacheHits << std::endl;
            std::cout << "  缓存未命中次数: " << metrics.cacheMisses << std::endl;
            std::cout << "  总更新时间: " << metrics.totalUpdateTime << " 秒" << std::endl;
            std::cout << "  平均位移: " << metrics.averageDisplacement << std::endl;
            std::cout << "  最大位移: " << metrics.maxDisplacement << std::endl;
            
            if (metrics.cacheHits + metrics.cacheMisses > 0)
            {
                double hitRate = (double)metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses) * 100;
                std::cout << "  缓存命中率: " << hitRate << "%" << std::endl;
            }
        }
        
        /**
         * @brief 测试静态模式性能
         */
        double testStaticMode()
        {
            auto startTime = std::chrono::high_resolution_clock::now();
            
            // 模拟10个时间步的静态重建
            for (int i = 0; i < 10; ++i)
            {
                oversetMesh->UpdateOGA();
            }
            
            auto endTime = std::chrono::high_resolution_clock::now();
            return std::chrono::duration<double>(endTime - startTime).count();
        }
        
        /**
         * @brief 测试动态模式性能
         */
        double testDynamicMode()
        {
            auto startTime = std::chrono::high_resolution_clock::now();
            
            // 模拟10个时间步的动态更新
            for (int i = 0; i < 10; ++i)
            {
                std::vector<Vector> displacements = generateDisplacementField(i);
                oversetMesh->UpdateMeshMotion(displacements, 0.01, i);
                oversetMesh->DynamicUpdateOGA();
            }
            
            auto endTime = std::chrono::high_resolution_clock::now();
            return std::chrono::duration<double>(endTime - startTime).count();
        }
        
        /**
         * @brief 测试基本接口兼容性
         */
        void testBasicInterfaceCompatibility()
        {
            // 测试基本的重叠网格接口
            oversetMesh->InitOGA();
            oversetMesh->UpdateOGA();
            oversetMesh->UpdateOversetField();
        }
        
        /**
         * @brief 测试动态网格接口
         */
        void testDynamicMeshInterface()
        {
            // 测试动态网格相关接口
            oversetMesh->EnableDynamicMesh(0.1, 1.0);
            
            std::vector<Vector> testDisplacements = generateDisplacementField(1);
            oversetMesh->UpdateMeshMotion(testDisplacements, 0.01, 1);
            
            bool result = oversetMesh->DynamicUpdateOGA();
            (void)result; // 避免未使用变量警告
            
            const auto& metrics = oversetMesh->GetDynamicMeshMetrics();
            (void)metrics; // 避免未使用变量警告
        }
        
        /**
         * @brief 测试数据一致性
         */
        void testDataConsistency()
        {
            // 这里应该添加数据一致性检查
            // 比如检查重叠网格装配前后的数据完整性
        }
        
        /**
         * @brief 测试并行兼容性
         */
        void testParallelCompatibility()
        {
            // 这里应该添加并行环境下的兼容性测试
            // 比如检查MPI通信是否正常
        }
    };
    
    /**
     * @brief 主测试函数
     */
    void RunDynamicOversetMeshTests(Package::FlowPackage* flowPackage)
    {
        DynamicOversetMeshExample example(flowPackage);
        
        std::cout << "开始动态重叠网格测试..." << std::endl;
        
        // 运行各种测试
        example.basicUsageExample();
        example.dynamicMeshUsageExample();
        example.performanceComparisonTest();
        example.compatibilityVerificationTest();
        
        std::cout << "\n所有测试完成！" << std::endl;
    }
}

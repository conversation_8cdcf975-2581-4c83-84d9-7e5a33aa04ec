#include "feilian-specialmodule/oversetMesh/OversetWallDistanceCalculator.h"
#include <algorithm>
#include <cmath>

namespace Overset
{
    // ==================== DynamicWallDistanceManager 实现 ====================

    class WallDistCalculator::DynamicWallDistanceManager
    {
    private:
        Scalar rebuildThreshold;
        double cacheValidityTime;
        double currentTime = 0.0;
        int currentStep = 0;
        std::vector<Vector> lastPositions;
        bool needsFullRebuild = false;

    public:
        DynamicWallDistanceManager(Scalar threshold, double validity)
            : rebuildThreshold(threshold), cacheValidityTime(validity) {}

        void updateMotionInfo(const std::vector<Vector> &displacements, double timeStep, int stepNumber)
        {
            currentTime += timeStep;
            currentStep = stepNumber;

            // 检查是否需要完全重建
            Scalar maxDisplacement = 0.0;
            for (const auto &disp : displacements)
            {
                maxDisplacement = std::max(maxDisplacement, disp.Mag());
            }

            if (maxDisplacement > rebuildThreshold)
            {
                needsFullRebuild = true;
            }
        }

        bool shouldRebuild() const { return needsFullRebuild; }
        void resetRebuildFlag() { needsFullRebuild = false; }
        double getCurrentTime() const { return currentTime; }
        int getCurrentStep() const { return currentStep; }
    };

    // ==================== IncrementalWallDistanceUpdater 实现 ====================

    class WallDistCalculator::IncrementalWallDistanceUpdater
    {
    private:
        Mesh *mesh;
        ZoneManager *zoneManager;
        Scalar updateThreshold;
        MotionType currentMotionType = RIGID_BODY;
        std::unordered_set<int> elementsToUpdate;

    public:
        IncrementalWallDistanceUpdater(Mesh *mesh_, ZoneManager *zoneManager_, Scalar threshold)
            : mesh(mesh_), zoneManager(zoneManager_), updateThreshold(threshold) {}

        void setMotionType(MotionType type) { currentMotionType = type; }

        bool performIncrementalUpdate(List<List<Scalar>> &wallDistances, List<List<int>> &nearestWallFaceID)
        {
            // 根据运动类型选择更新策略
            switch (currentMotionType)
            {
            case RIGID_BODY:
                return performRigidBodyUpdate(wallDistances, nearestWallFaceID);
            case DEFORMATION:
                return performDeformationUpdate(wallDistances, nearestWallFaceID);
            case MIXED:
                return performMixedUpdate(wallDistances, nearestWallFaceID);
            default:
                return false;
            }
        }

    private:
        bool performRigidBodyUpdate(List<List<Scalar>> &wallDistances, List<List<int>> &nearestWallFaceID)
        {
            // 刚体运动：可以通过坐标变换更新壁面距离
            // 这里简化实现，实际应该根据刚体变换矩阵更新
            return true; // 假设成功
        }

        bool performDeformationUpdate(List<List<Scalar>> &wallDistances, List<List<int>> &nearestWallFaceID)
        {
            // 变形运动：需要重新计算受影响区域
            identifyAffectedElements();
            if (elementsToUpdate.size() > mesh->GetElementNumberReal() * 0.5)
            {
                return false; // 影响范围太大，建议完全重建
            }

            // 这里应该调用实际的壁面距离计算
            return true; // 假设成功
        }

        bool performMixedUpdate(List<List<Scalar>> &wallDistances, List<List<int>> &nearestWallFaceID)
        {
            // 混合运动：结合刚体和变形的策略
            return performDeformationUpdate(wallDistances, nearestWallFaceID);
        }

        void identifyAffectedElements()
        {
            elementsToUpdate.clear();
            // 这里应该根据位移场识别需要更新的单元
            // 简化实现：假设所有单元都需要更新
            for (int elemID = 0; elemID < mesh->GetElementNumberReal(); ++elemID)
            {
                elementsToUpdate.insert(elemID);
            }
        }
    };

    // ==================== WallDistanceCache 实现 ====================

    class WallDistCalculator::WallDistanceCache
    {
    private:
        struct CachedWallDistance
        {
            List<Scalar> distances;
            List<int> nearestFaceIDs;
            std::chrono::time_point<std::chrono::steady_clock> timestamp;
            Vector lastPosition;
            Scalar confidence = 1.0;
        };

        std::unordered_map<int, CachedWallDistance> cache; // elemID -> CachedWallDistance
        double validityTime;

    public:
        WallDistanceCache(double validity) : validityTime(validity) {}

        bool tryGetCachedResult(int elemID, const Vector &currentPos,
                                List<Scalar> &distances, List<int> &faceIDs)
        {
            auto it = cache.find(elemID);
            if (it == cache.end())
            {
                return false;
            }

            const auto &cached = it->second;

            // 检查时间有效性
            auto now = std::chrono::high_resolution_clock::now();
            double elapsed = std::chrono::duration<double>(now - cached.timestamp).count();
            if (elapsed > validityTime)
            {
                cache.erase(it);
                return false;
            }

            // 检查位置变化
            Scalar positionChange = (currentPos - cached.lastPosition).Mag();
            if (positionChange > 0.01) // 位置变化阈值
            {
                return false;
            }

            distances = cached.distances;
            faceIDs = cached.nearestFaceIDs;
            return true;
        }

        void cacheResult(int elemID, const List<Scalar> &distances, const List<int> &faceIDs,
                         const Vector &position, Scalar confidence = 1.0)
        {
            CachedWallDistance cached;
            cached.distances = distances;
            cached.nearestFaceIDs = faceIDs;
            cached.timestamp = std::chrono::high_resolution_clock::now();
            cached.lastPosition = position;
            cached.confidence = confidence;

            cache[elemID] = std::move(cached);
        }

        void invalidateByMotion(const std::vector<Vector> &displacements, Scalar threshold)
        {
            for (auto it = cache.begin(); it != cache.end();)
            {
                int elemID = it->first;
                if (elemID < displacements.size() && displacements[elemID].Mag() > threshold)
                {
                    it = cache.erase(it);
                }
                else
                {
                    ++it;
                }
            }
        }

        void cleanupExpiredCache()
        {
            auto now = std::chrono::high_resolution_clock::now();
            for (auto it = cache.begin(); it != cache.end();)
            {
                double elapsed = std::chrono::duration<double>(now - it->second.timestamp).count();
                if (elapsed > validityTime)
                {
                    it = cache.erase(it);
                }
                else
                {
                    ++it;
                }
            }
        }

        size_t getCacheSize() const { return cache.size(); }
    };

    // ==================== TimeStepInheritanceManager 实现 ====================

    class WallDistCalculator::TimeStepInheritanceManager
    {
    private:
        struct InheritanceInfo
        {
            std::unordered_map<int, List<Scalar>> previousWallDistances;
            std::unordered_map<int, List<int>> previousNearestFaceIDs;
            std::vector<Vector> previousPositions;
            double timeStep = 0.0;
            int stepNumber = 0;
        };

        InheritanceInfo previousStep;
        Scalar inheritanceThreshold = 0.8;

    public:
        bool predictFromPreviousStep(int elemID, const Vector &currentPos,
                                     const Vector &velocity, double dt,
                                     List<Scalar> &predictedDistances, List<int> &predictedFaceIDs)
        {
            auto distIt = previousStep.previousWallDistances.find(elemID);
            auto faceIt = previousStep.previousNearestFaceIDs.find(elemID);

            if (distIt == previousStep.previousWallDistances.end() ||
                faceIt == previousStep.previousNearestFaceIDs.end())
            {
                return false;
            }

            // 简单的线性预测
            Vector predictedPosition = currentPos + velocity * dt;

            // 检查预测的合理性
            if (elemID < previousStep.previousPositions.size())
            {
                Vector positionChange = predictedPosition - previousStep.previousPositions[elemID];
                if (positionChange.Mag() > inheritanceThreshold)
                {
                    return false; // 位置变化太大，预测不可靠
                }
            }

            predictedDistances = distIt->second;
            predictedFaceIDs = faceIt->second;

            // 根据位移调整距离（简化处理）
            Vector displacement = currentPos - previousStep.previousPositions[elemID];
            Scalar displacementMag = displacement.Mag();

            for (auto &dist : predictedDistances)
            {
                // 简单的距离调整，实际应该更复杂
                dist += displacementMag * 0.1; // 假设距离略有增加
            }

            return true;
        }

        void saveCurrentStep(const std::unordered_map<int, List<Scalar>> &wallDistances,
                             const std::unordered_map<int, List<int>> &nearestFaceIDs,
                             const std::vector<Vector> &positions, double dt, int step)
        {
            previousStep.previousWallDistances = wallDistances;
            previousStep.previousNearestFaceIDs = nearestFaceIDs;
            previousStep.previousPositions = positions;
            previousStep.timeStep = dt;
            previousStep.stepNumber = step;
        }

        bool validatePrediction(const List<Scalar> &predicted, const List<Scalar> &actual) const
        {
            if (predicted.size() != actual.size())
            {
                return false;
            }

            Scalar totalError = 0.0;
            for (size_t i = 0; i < predicted.size(); ++i)
            {
                Scalar relativeError = std::abs(predicted[i] - actual[i]) / (actual[i] + 1e-12);
                totalError += relativeError;
            }

            Scalar averageError = totalError / predicted.size();
            return averageError < inheritanceThreshold;
        }

        void setInheritanceThreshold(Scalar threshold) { inheritanceThreshold = threshold; }
    };

    // ==================== DynamicLoadBalancer 实现 ====================

    class WallDistCalculator::DynamicLoadBalancer
    {
    private:
        struct ProcessorLoadInfo
        {
            size_t calculationCount = 0;
            double calculationTime = 0.0;
            size_t elementCount = 0;
            std::chrono::time_point<std::chrono::steady_clock> lastUpdate;
        };

        std::vector<ProcessorLoadInfo> processorLoads;
        double rebalanceThreshold = 0.3;

    public:
        DynamicLoadBalancer(int nProcs) : processorLoads(nProcs) {}

        void updateProcessorLoad(int procID, size_t calculations, double time)
        {
            if (procID >= 0 && procID < processorLoads.size())
            {
                processorLoads[procID].calculationCount += calculations;
                processorLoads[procID].calculationTime += time;
                processorLoads[procID].lastUpdate = std::chrono::high_resolution_clock::now();
            }
        }

        bool needsRebalancing() const
        {
            if (processorLoads.size() < 2)
            {
                return false;
            }

            double maxLoad = 0.0, minLoad = INF;
            for (const auto &load : processorLoads)
            {
                double avgTime = load.calculationCount > 0 ? load.calculationTime / load.calculationCount : 0.0;
                maxLoad = std::max(maxLoad, avgTime);
                minLoad = std::min(minLoad, avgTime);
            }

            return (maxLoad - minLoad) / (maxLoad + 1e-12) > rebalanceThreshold;
        }

        int selectOptimalProcessor(const Vector &position) const
        {
            // 简化实现：选择负载最轻的处理器
            int optimalProc = 0;
            double minLoad = INF;

            for (int i = 0; i < processorLoads.size(); ++i)
            {
                const auto &load = processorLoads[i];
                double avgTime = load.calculationCount > 0 ? load.calculationTime / load.calculationCount : 0.0;
                if (avgTime < minLoad)
                {
                    minLoad = avgTime;
                    optimalProc = i;
                }
            }

            return optimalProc;
        }

        void resetLoadInfo()
        {
            for (auto &load : processorLoads)
            {
                load = ProcessorLoadInfo{};
            }
        }
    };

} // namespace Overset

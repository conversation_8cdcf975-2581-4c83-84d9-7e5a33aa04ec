////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OversetPerformanceMonitor.h
//! <AUTHOR>
//! @brief 重叠网格性能监控器
//! @date 2024-03-12
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_OversetPerformanceMonitor_
#define _specialModule_oversetMesh_OversetPerformanceMonitor_

#include <unordered_map>
#include <chrono>

namespace Overset
{
    /**
     * @brief 重叠网格性能监控器
     * 
     * 负责收集和统计搜索性能指标，提供性能分析功能
     */
    class OversetPerformanceMonitor
    {
    public:
        /**
         * @brief 性能指标结构
         */
        struct SearchMetrics
        {
            size_t totalSearches = 0;
            size_t successfulSearches = 0;
            size_t failedSearches = 0;
            double totalSearchTime = 0.0;
            double averageSearchTime = 0.0;
            double maxSearchTime = 0.0;
            std::unordered_map<int, size_t> zoneSearchCounts;
            std::unordered_map<int, size_t> zoneSuccessCounts;
            
            double getSuccessRate() const
            {
                return totalSearches > 0 ? (double)successfulSearches / totalSearches : 0.0;
            }
            
            void reset()
            {
                *this = SearchMetrics{};
            }
        };
        
        /**
         * @brief 并行通信指标结构
         */
        struct CommunicationMetrics
        {
            size_t totalMessages = 0;
            size_t totalDataTransferred = 0;
            double totalCommTime = 0.0;
            double averageCommTime = 0.0;
            double maxCommTime = 0.0;
            
            void reset()
            {
                *this = CommunicationMetrics{};
            }
        };

    public:
        /**
         * @brief 构造函数
         */
        OversetPerformanceMonitor() = default;
        
        /**
         * @brief 析构函数
         */
        ~OversetPerformanceMonitor() = default;
        
        /**
         * @brief 记录搜索开始
         * @return 搜索开始时间点
         */
        std::chrono::high_resolution_clock::time_point recordSearchStart();
        
        /**
         * @brief 记录搜索结束
         * @param startTime 搜索开始时间点
         * @param successful 是否搜索成功
         * @param zoneID 搜索的子域ID
         */
        void recordSearchEnd(const std::chrono::high_resolution_clock::time_point& startTime,
                           bool successful, int zoneID);
        
        /**
         * @brief 记录通信开始
         * @return 通信开始时间点
         */
        std::chrono::high_resolution_clock::time_point recordCommStart();
        
        /**
         * @brief 记录通信结束
         * @param startTime 通信开始时间点
         * @param dataSize 传输的数据大小
         */
        void recordCommEnd(const std::chrono::high_resolution_clock::time_point& startTime,
                         size_t dataSize);
        
        /**
         * @brief 获取搜索性能指标
         */
        const SearchMetrics& getSearchMetrics() const { return searchMetrics; }
        
        /**
         * @brief 获取通信性能指标
         */
        const CommunicationMetrics& getCommunicationMetrics() const { return commMetrics; }
        
        /**
         * @brief 重置所有性能指标
         */
        void resetAllMetrics();
        
        /**
         * @brief 打印性能报告
         */
        void printPerformanceReport() const;
        
        /**
         * @brief 导出性能数据到文件
         * @param filename 文件名
         */
        void exportMetricsToFile(const std::string& filename) const;

    private:
        SearchMetrics searchMetrics;           // 搜索性能指标
        CommunicationMetrics commMetrics;      // 通信性能指标
    };
}

#endif

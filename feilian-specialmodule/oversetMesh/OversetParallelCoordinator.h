////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OversetParallelCoordinator.h
//! <AUTHOR>
//! @brief 重叠网格并行协调器
//! @date 2024-03-12
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_OversetParallelCoordinator_
#define _specialModule_oversetMesh_OversetParallelCoordinator_

#include "feilian-specialmodule/oversetMesh/Acceptor.h"
#include "feilian-specialmodule/oversetMesh/OverDefines.h"
#include "feilian-specialmodule/oversetMesh/OversetPerformanceMonitor.h"
#include "meshProcess/zone/ZoneManager.h"
#include "basic/mesh/Mesh.h"
#include "feilian-external/linux-gcc7.3.0/boost/include/boost/mpi.hpp"
#include <functional>

namespace Overset
{
    /**
     * @brief 重叠网格并行协调器
     * 
     * 负责并行搜索的协调、分组策略和MPI通信管理
     */
    class OversetParallelCoordinator
    {
    public:
        /**
         * @brief 分组策略枚举
         */
        enum class GroupingStrategy
        {
            BASIC,          // 基础分组策略
            SMART,          // 智能分组策略（考虑负载均衡）
            DYNAMIC         // 动态分组策略（适用于动态网格）
        };
        
        /**
         * @brief 搜索函数类型定义
         */
        using SearchFunction = std::function<void(List<Acceptor>&)>;

    public:
        /**
         * @brief 构造函数
         * @param mpi_world MPI通信器
         * @param zoneManager 域管理器指针
         * @param localMesh 本地网格指针
         * @param performanceMonitor 性能监控器指针
         */
        OversetParallelCoordinator(const boost::mpi::communicator& mpi_world,
                                 ZoneManager* zoneManager,
                                 Mesh* localMesh,
                                 OversetPerformanceMonitor* performanceMonitor = nullptr);
        
        /**
         * @brief 析构函数
         */
        ~OversetParallelCoordinator() = default;
        
        /**
         * @brief 执行并行搜索
         * @param groupedAcpts 分组的插值单元列表
         * @param searchResults 搜索结果
         * @param searchFunction 搜索函数
         */
        void executeParallelSearch(List<List<Acceptor>>& groupedAcpts,
                                 Set<Acceptor>& searchResults,
                                 const SearchFunction& searchFunction);
        
        /**
         * @brief 将插值单元按照可能存在贡献单元的进程分组
         * @param searchElemID 需要搜索贡献单元的单元编号集合
         * @param groupedAcpts 分组结果
         * @param strategy 分组策略
         * @param globalTreeInfo 全局树信息
         */
        void groupAcceptors(Set<int>& searchElemID,
                          List<List<Acceptor>>& groupedAcpts,
                          GroupingStrategy strategy,
                          const std::vector<TreeInfo>& globalTreeInfo);
        
        /**
         * @brief 将插值单元按照贡献单元的进程号分组
         * @param srcAcpts 源插值单元集合
         * @param groupedAcpts 分组结果
         */
        void groupAcceptorsByDonor(const Set<Acceptor>& srcAcpts,
                                 List<List<Acceptor>>& groupedAcpts);
        
        /**
         * @brief 设置分组策略
         * @param strategy 分组策略
         */
        void setGroupingStrategy(GroupingStrategy strategy) { currentStrategy = strategy; }
        
        /**
         * @brief 获取当前分组策略
         */
        GroupingStrategy getGroupingStrategy() const { return currentStrategy; }

    private:
        /**
         * @brief 基础分组策略实现
         */
        void basicGrouping(Set<int>& searchElemID,
                         List<List<Acceptor>>& groupedAcpts,
                         const std::vector<TreeInfo>& globalTreeInfo);
        
        /**
         * @brief 智能分组策略实现
         */
        void smartGrouping(Set<int>& searchElemID,
                         List<List<Acceptor>>& groupedAcpts,
                         const std::vector<TreeInfo>& globalTreeInfo);
        
        /**
         * @brief 动态分组策略实现
         */
        void dynamicGrouping(Set<int>& searchElemID,
                           List<List<Acceptor>>& groupedAcpts,
                           const std::vector<TreeInfo>& globalTreeInfo);
        
        /**
         * @brief 判断单元中心是否在树的空间范围内
         */
        bool elemCenterInTree(int elemID, const TreeInfo& treeInfo) const;
        
        /**
         * @brief 异步发送搜索请求
         */
        void sendSearchRequests(const List<List<Acceptor>>& groupedAcpts);
        
        /**
         * @brief 处理接收到的搜索请求
         */
        void processIncomingRequests(const SearchFunction& searchFunction);
        
        /**
         * @brief 收集搜索结果
         */
        void collectSearchResults(Set<Acceptor>& searchResults,
                                const List<List<Acceptor>>& groupedAcpts);

    private:
        const boost::mpi::communicator& mpi_world;  // MPI通信器
        ZoneManager* zoneManager;                   // 域管理器指针
        Mesh* localMesh;                           // 本地网格指针
        OversetPerformanceMonitor* perfMonitor;    // 性能监控器指针
        
        int processorID;                           // 当前进程ID
        int nProcessor;                            // 总进程数
        int dim;                                   // 网格维度
        
        GroupingStrategy currentStrategy = GroupingStrategy::BASIC; // 当前分组策略
        
        // 异步通信管理
        std::vector<boost::mpi::request> pendingRequests;
    };
}

#endif

////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OversetDynamicMeshManager.h
//! <AUTHOR>
//! @brief 重叠网格动态网格管理器
//! @date 2024-03-12
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_OversetDynamicMeshManager_
#define _specialModule_oversetMesh_OversetDynamicMeshManager_

#include "feilian-specialmodule/oversetMesh/Acceptor.h"
#include "basic/mesh/Mesh.h"
#include "basic/common/ConfigUtility.h"
#include <unordered_map>
#include <chrono>
#include <memory>

namespace Overset
{
    /**
     * @brief 重叠网格动态网格管理器
     * 
     * 负责动态网格支持、搜索结果缓存、时间步继承和增量更新管理
     */
    class OversetDynamicMeshManager
    {
    public:
        /**
         * @brief 网格运动类型
         */
        enum class MotionType
        {
            RIGID_BODY,     // 刚体运动
            DEFORMATION,    // 变形运动
            MIXED          // 混合运动
        };
        
        /**
         * @brief 动态网格统计指标
         */
        struct DynamicMeshMetrics
        {
            size_t incrementalUpdates = 0;
            size_t fullRebuilds = 0;
            size_t cacheHits = 0;
            size_t cacheMisses = 0;
            size_t inheritanceHits = 0;
            double totalUpdateTime = 0.0;
            double averageDisplacement = 0.0;
            double maxDisplacement = 0.0;
            
            void reset()
            {
                *this = DynamicMeshMetrics{};
            }
        };

    public:
        /**
         * @brief 构造函数
         * @param rebuildThreshold 重建阈值
         * @param cacheValidityTime 缓存有效时间（秒）
         */
        OversetDynamicMeshManager(Scalar rebuildThreshold = 0.1, 
                                double cacheValidityTime = 1.0);
        
        /**
         * @brief 析构函数
         */
        ~OversetDynamicMeshManager() = default;
        
        /**
         * @brief 启用动态网格模式
         * @param numElements 网格单元数量
         */
        void enableDynamicMesh(int numElements);
        
        /**
         * @brief 禁用动态网格模式
         */
        void disableDynamicMesh();
        
        /**
         * @brief 更新网格运动信息
         * @param displacementField 位移场
         * @param timeStep 时间步长
         * @param stepNumber 时间步编号
         */
        void updateMeshMotion(const std::vector<Vector>& displacementField,
                            double timeStep, int stepNumber);
        
        /**
         * @brief 判断指定子域是否需要重建KDT树
         * @param zoneID 子域编号
         * @param zoneStartID 子域起始单元ID
         * @param zoneElemNum 子域单元数量
         * @return 是否需要重建
         */
        bool shouldRebuildKDT(int zoneID, int zoneStartID, int zoneElemNum) const;
        
        /**
         * @brief 尝试从缓存获取搜索结果
         * @param acceptorID 插值单元ID
         * @param currentPos 当前位置
         * @param result 输出结果
         * @return 是否成功从缓存获取
         */
        bool tryGetCachedResult(int acceptorID, const Vector& currentPos, Acceptor& result);
        
        /**
         * @brief 缓存搜索结果
         * @param result 搜索结果
         * @param position 位置
         * @param confidence 置信度
         */
        void cacheSearchResult(const Acceptor& result, const Vector& position, 
                             Scalar confidence = 1.0);
        
        /**
         * @brief 尝试从时间步继承预测结果
         * @param acceptorID 插值单元ID
         * @param currentPos 当前位置
         * @param result 输出结果
         * @return 是否成功预测
         */
        bool tryInheritFromPreviousStep(int acceptorID, const Vector& currentPos, Acceptor& result);
        
        /**
         * @brief 保存当前时间步的搜索结果
         * @param results 搜索结果映射
         * @param positions 位置向量
         */
        void saveCurrentStepResults(const std::unordered_map<int, Acceptor>& results,
                                  const std::vector<Vector>& positions);
        
        /**
         * @brief 分析网格运动类型
         * @param displacements 位移场
         * @return 运动类型
         */
        MotionType analyzeMotionType(const std::vector<Vector>& displacements) const;
        
        /**
         * @brief 获取动态网格统计指标
         */
        const DynamicMeshMetrics& getMetrics() const { return metrics; }
        
        /**
         * @brief 重置统计指标
         */
        void resetMetrics() { metrics.reset(); }
        
        /**
         * @brief 是否启用了动态网格
         */
        bool isDynamicMeshEnabled() const { return isEnabled; }
        
        /**
         * @brief 获取当前时间步
         */
        double getCurrentTimeStep() const { return currentTimeStep; }
        
        /**
         * @brief 获取当前步数
         */
        int getCurrentStepNumber() const { return currentStepNumber; }

    private:
        // 前向声明内部类
        class SearchResultCache;
        class TimeStepInheritanceManager;
        class IncrementalKDTManager;
        
        /**
         * @brief 计算单元位移幅度
         */
        void calculateElementDisplacements(const std::vector<Vector>& displacements);
        
        /**
         * @brief 计算单元速度
         */
        Vector calculateVelocity(int elemID) const;

    private:
        bool isEnabled = false;                    // 是否启用动态网格
        Scalar rebuildThreshold;                   // 重建阈值
        double cacheValidityTime;                  // 缓存有效时间
        double currentTimeStep = 0.0;              // 当前时间步长
        int currentStepNumber = 0;                 // 当前步数
        
        // 网格运动信息
        std::vector<Vector> previousPositions;     // 前一时间步位置
        std::vector<Vector> currentDisplacements;  // 当前位移
        std::vector<Scalar> elementDisplacements; // 单元位移幅度
        
        // 内部管理器
        std::unique_ptr<SearchResultCache> resultCache;
        std::unique_ptr<TimeStepInheritanceManager> inheritanceManager;
        std::unique_ptr<IncrementalKDTManager> kdtUpdateManager;
        
        // 统计指标
        mutable DynamicMeshMetrics metrics;
    };
}

#endif
